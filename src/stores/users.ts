import { defineStore } from 'pinia';
import { ref } from 'vue';
import { CreateUserInput, GetUsersInput, PrivilegeOption, UpdateUserInput, User } from '../services/users/types';
import { ApiErrorResponse, ApiResponse, PagedResource } from '../services/types';
import { API } from '../services';
import { AxiosError } from 'axios';

export const useUsersStore = defineStore('usersStore', () => {
    const users = ref<User[]>([]);
    const totalElements = ref<number>(0);
    const totalPages = ref<number>(0);
    const pageSize = ref<number | undefined>(undefined);
    const pageElements = ref<number | undefined>(undefined);
    const pageNumber = ref<number | undefined>(undefined);

    function initUsers(data: PagedResource<User>) {
        users.value = data.content;
        totalElements.value = data.totalElements;
        totalPages.value = data.totalPages;
        pageSize.value = data.pageSize;
        pageNumber.value = data.pageNumber;
        pageElements.value = data.pageElements;
    }

    function addNewUser(user: User) {
        users.value.push(user);
    }

    function updateUser(user: User) {
        const idx = users.value.findIndex((s) => s.id === user.id);
        if (idx === -1) return;
        users.value.splice(idx, 1, user);
    }

    async function dispatchGetUsers(input: GetUsersInput): Promise<ApiResponse<PagedResource<User>>> {
        try {
            const { status, data } = await API.users.getUsers(input);
            initUsers(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchGetUser(userId: string): Promise<ApiResponse<User>> {
        try {
            const { status, data } = await API.users.getUser(userId);
            updateUser(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchCreateUser(input: CreateUserInput, profilePicture?: File): Promise<ApiResponse<User>> {
        try {
            const { status, data } = await API.users.createUser(input, profilePicture);
            addNewUser(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchUpdateUser(input: UpdateUserInput): Promise<ApiResponse<User>> {
        try {
            const { status, data } = await API.users.updateUser(input);
            updateUser(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }



    /**
     * Returns options for the privilege selection drop down for creating or updating users.
     * - The options are enabled/disabled for selection based on the privileges of the authenticated user.
     * - When userId is provided, the privilege options are marked as selected if the user already has those,
     *   this is used from the update-user screen.
     * @param userId
     */
    async function dispatchGetPrivilegeOptions(userId?: string): Promise<ApiResponse<PrivilegeOption[]>> {
        try {
            const { status, data } = await API.users.getPrivilegeOptions(userId);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    return {
        users,
        totalElements,
        totalPages,
        pageSize,
        pageNumber,
        pageElements,
        dispatchGetUsers,
        dispatchGetUser,
        dispatchCreateUser,
        dispatchUpdateUser,
        dispatchGetPrivilegeOptions
    };
});
